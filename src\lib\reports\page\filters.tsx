'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { CalendarIcon, Download, Save, Trash2, Filter as FilterIcon } from 'lucide-react';
import { format } from 'date-fns';
import { DateRange } from 'react-day-picker';

import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

// Enhanced filter options with real data structure
const dateRangeOptions = [
  { label: 'All Data', span: [new Date(0), new Date()] },
  { label: 'Today', span: [new Date(), new Date()] },
  { label: 'Yesterday', span: [new Date(Date.now() - 86400000), new Date(Date.now() - 86400000)] },
  { label: 'This Week', span: [new Date(Date.now() - 7 * 86400000), new Date()] },
  { label: 'Last Week', span: [new Date(Date.now() - 14 * 86400000), new Date(Date.now() - 7 * 86400000)] },
  { label: 'This Month', span: [new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date()] },
  {
    label: 'Last Month',
    span: [
      new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
      new Date(new Date().getFullYear(), new Date().getMonth(), 0),
    ],
  },
  {
    label: 'This Quarter',
    span: [new Date(new Date().getFullYear(), Math.floor(new Date().getMonth() / 3) * 3, 1), new Date()],
  },
  { label: 'This Year', span: [new Date(new Date().getFullYear(), 0, 1), new Date()] },
  {
    label: 'Last Year',
    span: [new Date(new Date().getFullYear() - 1, 0, 1), new Date(new Date().getFullYear() - 1, 11, 31)],
  },
  { label: 'Rolling 7 Days', span: [new Date(Date.now() - 7 * 86400000), new Date()] },
  { label: 'Rolling 30 Days', span: [new Date(Date.now() - 30 * 86400000), new Date()] },
  { label: 'Rolling 90 Days', span: [new Date(Date.now() - 90 * 86400000), new Date()] },
  { label: 'Rolling 365 Days', span: [new Date(Date.now() - 365 * 86400000), new Date()] },
  { label: 'Custom Range', span: [new Date(), new Date()] },
];

// Location and project status options will be provided by props from server component

// Filter presets will be loaded from user preferences or server

interface FilterState {
  dateRange: string;
  customDateRange?: DateRange;
  locations: string[];
  customers: string[];
  projectStatus: string[];
  amountMin?: number;
  amountMax?: number;
}

interface FilterPreset {
  id: string;
  name: string;
  description: string;
  filters: Partial<FilterState>;
}

interface FilterOption {
  value: string;
  label: string;
}

interface FiltersClientProps {
  customerOptions: FilterOption[];
  locationOptions: FilterOption[];
  projectStatusOptions: FilterOption[];
}

function FiltersClient({ customerOptions, locationOptions, projectStatusOptions }: FiltersClientProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Initialize filter state from URL parameters
  const initializeFilters = (): FilterState => ({
    dateRange: searchParams.get('dateRange') || 'All Data',
    customDateRange:
      searchParams.get('startDate') && searchParams.get('endDate')
        ? {
            from: new Date(searchParams.get('startDate')!),
            to: new Date(searchParams.get('endDate')!),
          }
        : undefined,
    locations: searchParams.get('locations')?.split(',').filter(Boolean) || [],
    customers: searchParams.get('customers')?.split(',').filter(Boolean) || [],
    projectStatus: searchParams.get('projectStatus')?.split(',').filter(Boolean) || [],
    amountMin: searchParams.get('amountMin') ? Number(searchParams.get('amountMin')) : undefined,
    amountMax: searchParams.get('amountMax') ? Number(searchParams.get('amountMax')) : undefined,
  });

  const [filters, setFilters] = useState<FilterState>(initializeFilters);
  const [showLocationDropdown, setShowLocationDropdown] = useState(false);
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [showCustomDatePicker, setShowCustomDatePicker] = useState(false);
  const [showPresetDialog, setShowPresetDialog] = useState(false);

  const [newPresetName, setNewPresetName] = useState('');
  const [newPresetDescription, setNewPresetDescription] = useState('');
  const [filterPresets, setFilterPresets] = useState<FilterPreset[]>([]);

  // Load saved presets from localStorage on mount
  useEffect(() => {
    const savedPresets = localStorage.getItem('reportFilterPresets');
    if (savedPresets) {
      try {
        const parsed = JSON.parse(savedPresets);
        setFilterPresets(parsed);
      } catch (error) {
        // Failed to load saved presets
      }
    }
  }, []);

  // Helper functions for filter management
  const handleMultiSelectToggle = (value: string, field: keyof FilterState) => {
    setFilters((prev) => ({
      ...prev,
      [field]: Array.isArray(prev[field])
        ? (prev[field] as string[]).includes(value)
          ? (prev[field] as string[]).filter((item) => item !== value)
          : [...(prev[field] as string[]), value]
        : [value],
    }));
  };

  const handleDateRangeChange = (dateRange: string) => {
    if (dateRange === 'Custom Range') {
      setShowCustomDatePicker(true);
      setFilters((prev) => ({ ...prev, dateRange }));
    } else {
      setFilters((prev) => ({ ...prev, dateRange, customDateRange: undefined }));
      setShowCustomDatePicker(false);
    }
  };

  const handleCustomDateRangeChange = (range: DateRange | undefined) => {
    setFilters((prev) => ({ ...prev, customDateRange: range }));
  };

  const handleAmountChange = (field: 'amountMin' | 'amountMax', value: string) => {
    const numValue = value === '' ? undefined : Number(value);
    setFilters((prev) => ({ ...prev, [field]: numValue }));
  };

  const applyFilters = () => {
    const params = new URLSearchParams();

    // Handle date range
    if (filters.dateRange && filters.dateRange !== 'All Data') {
      if (filters.dateRange === 'Custom Range' && filters.customDateRange?.from && filters.customDateRange?.to) {
        params.set('startDate', filters.customDateRange.from.toISOString());
        params.set('endDate', filters.customDateRange.to.toISOString());
        params.set('dateRange', 'Custom Range');
      } else {
        const selectedRange = dateRangeOptions.find((r) => r.label === filters.dateRange);
        if (selectedRange) {
          params.set('startDate', selectedRange.span[0].toISOString());
          params.set('endDate', selectedRange.span[1].toISOString());
          params.set('dateRange', filters.dateRange);
        }
      }
    }

    // Handle other filters
    if (filters.locations.length > 0) params.set('locations', filters.locations.join(','));
    if (filters.customers.length > 0) params.set('customers', filters.customers.join(','));
    if (filters.projectStatus.length > 0) params.set('projectStatus', filters.projectStatus.join(','));
    if (filters.amountMin !== undefined) params.set('amountMin', filters.amountMin.toString());
    if (filters.amountMax !== undefined) params.set('amountMax', filters.amountMax.toString());

    // Navigate to the same page with new parameters
    router.push(`${pathname}?${params.toString()}`);
  };

  const clearFilters = () => {
    setFilters({
      dateRange: 'All Data',
      locations: [],
      customers: [],
      projectStatus: [],
      amountMin: undefined,
      amountMax: undefined,
    });
    router.push(pathname);
  };

  const loadPreset = (preset: FilterPreset) => {
    setFilters((prev) => ({
      ...prev,
      ...preset.filters,
      customDateRange: undefined, // Reset custom date range when loading preset
    }));
    setShowPresetDialog(false);
  };

  const savePreset = () => {
    if (!newPresetName.trim()) return;

    const newPreset: FilterPreset = {
      id: `custom-${Date.now()}`,
      name: newPresetName.trim(),
      description: newPresetDescription.trim(),
      filters: { ...filters },
    };

    const updatedPresets = [...filterPresets, newPreset];

    localStorage.setItem('reportFilterPresets', JSON.stringify(updatedPresets));
    setFilterPresets(updatedPresets);

    setNewPresetName('');
    setNewPresetDescription('');
    setShowSavePresetDialog(false);
  };

  const deletePreset = (presetId: string) => {
    const updatedPresets = filterPresets.filter((p) => p.id !== presetId);
    localStorage.setItem('reportFilterPresets', JSON.stringify(updatedPresets));
    setFilterPresets(updatedPresets);
  };

  // Export functionality
  const exportToCSV = async () => {
    try {
      // In a real implementation, this would call an API endpoint with current filters
      const response = await fetch(
        `/api/reports/export?format=csv&${new URLSearchParams(
          Object.fromEntries(
            Object.entries({
              dateRange: filters.dateRange,
              locations: filters.locations.join(','),
              customers: filters.customers.join(','),
              projectStatus: filters.projectStatus.join(','),
              amountMin: filters.amountMin?.toString() || '',
              amountMax: filters.amountMax?.toString() || '',
              startDate: filters.customDateRange?.from?.toISOString() || '',
              endDate: filters.customDateRange?.to?.toISOString() || '',
            }).filter(([, value]) => value !== ''),
          ),
        )}`,
      );

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `report-${format(new Date(), 'yyyy-MM-dd')}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      // Export failed
      // Create empty CSV template
      const csvContent = generateEmptyCSV();
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `report-template-${format(new Date(), 'yyyy-MM-dd')}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    }
  };

  const exportToPDF = async () => {
    try {
      // In a real implementation, this would call an API endpoint
      const response = await fetch(
        `/api/reports/export?format=pdf&${new URLSearchParams(
          Object.fromEntries(
            Object.entries({
              dateRange: filters.dateRange,
              locations: filters.locations.join(','),
              customers: filters.customers.join(','),
              projectStatus: filters.projectStatus.join(','),
              amountMin: filters.amountMin?.toString() || '',
              amountMax: filters.amountMax?.toString() || '',
            }).filter(([, value]) => value !== ''),
          ),
        )}`,
      );

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `report-${format(new Date(), 'yyyy-MM-dd')}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      // PDF export failed
      alert('PDF export functionality requires server-side implementation');
    }
  };

  const generateEmptyCSV = () => {
    const headers = ['Date', 'Customer', 'Project', 'Status', 'Amount', 'Location'];
    return headers.join(',') + '\n';
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FilterIcon className="h-5 w-5" />
              Report Filters
            </CardTitle>
            <CardDescription>Filter and customize your report data</CardDescription>
          </div>
          <div className="flex gap-2">
            <Dialog open={showPresetDialog} onOpenChange={setShowPresetDialog}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Save className="h-4 w-4 mr-2" />
                  Presets
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Filter Presets</DialogTitle>
                  <DialogDescription>Load saved filter combinations or create new ones</DialogDescription>
                </DialogHeader>
                <Tabs defaultValue="load" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="load">Load Preset</TabsTrigger>
                    <TabsTrigger value="save">Save Current</TabsTrigger>
                  </TabsList>
                  <TabsContent value="load" className="space-y-4">
                    <div className="grid gap-3 max-h-96 overflow-y-auto">
                      {filterPresets.map((preset) => (
                        <div key={preset.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex-1">
                            <div className="font-medium">{preset.name}</div>
                            <div className="text-sm text-muted-foreground">{preset.description}</div>
                          </div>
                          <div className="flex gap-2">
                            <Button size="sm" onClick={() => loadPreset(preset)}>
                              Load
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => deletePreset(preset.id)}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                  <TabsContent value="save" className="space-y-4">
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="preset-name">Preset Name</Label>
                        <Input
                          id="preset-name"
                          value={newPresetName}
                          onChange={(e) => setNewPresetName(e.target.value)}
                          placeholder="Enter preset name"
                        />
                      </div>
                      <div>
                        <Label htmlFor="preset-description">Description</Label>
                        <Input
                          id="preset-description"
                          value={newPresetDescription}
                          onChange={(e) => setNewPresetDescription(e.target.value)}
                          placeholder="Enter preset description"
                        />
                      </div>
                      <Button onClick={savePreset} disabled={!newPresetName.trim()}>
                        Save Preset
                      </Button>
                    </div>
                  </TabsContent>
                </Tabs>
              </DialogContent>
            </Dialog>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-48">
                <div className="space-y-2">
                  <Button variant="ghost" className="w-full justify-start" onClick={exportToCSV}>
                    Export as CSV
                  </Button>
                  <Button variant="ghost" className="w-full justify-start" onClick={exportToPDF}>
                    Export as PDF
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Main Filter Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Date Range Filter */}
          <div className="space-y-2">
            <Label>Date Range</Label>
            <Select value={filters.dateRange} onValueChange={handleDateRangeChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select date range" />
              </SelectTrigger>
              <SelectContent>
                {dateRangeOptions.map((range) => (
                  <SelectItem key={range.label} value={range.label}>
                    {range.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Custom Date Range Picker */}
            {showCustomDatePicker && (
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !filters.customDateRange && 'text-muted-foreground',
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.customDateRange?.from ? (
                      filters.customDateRange.to ? (
                        <>
                          {format(filters.customDateRange.from, 'LLL dd, y')} -{' '}
                          {format(filters.customDateRange.to, 'LLL dd, y')}
                        </>
                      ) : (
                        format(filters.customDateRange.from, 'LLL dd, y')
                      )
                    ) : (
                      <span>Pick a date range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="range"
                    defaultMonth={filters.customDateRange?.from}
                    selected={filters.customDateRange}
                    onSelect={handleCustomDateRangeChange}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
            )}
          </div>

          {/* Location Filter */}
          <div className="space-y-2">
            <Label>Locations</Label>
            <Popover open={showLocationDropdown} onOpenChange={setShowLocationDropdown}>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left font-normal">
                  {filters.locations.length === 0 ? (
                    <span className="text-muted-foreground">All Locations</span>
                  ) : (
                    <span>{filters.locations.length} location(s) selected</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0" align="start">
                <div className="p-3 border-b">
                  <div className="text-sm font-medium">Select Locations</div>
                </div>
                <div className="max-h-60 overflow-y-auto">
                  {locationOptions.map((location) => (
                    <div
                      key={location.value}
                      className="flex items-center space-x-2 p-2 hover:bg-muted cursor-pointer"
                      onClick={() => handleMultiSelectToggle(location.value, 'locations')}
                    >
                      <Checkbox
                        checked={filters.locations.includes(location.value)}
                        onChange={() => handleMultiSelectToggle(location.value, 'locations')}
                      />
                      <span className="text-sm">{location.label}</span>
                    </div>
                  ))}
                </div>
                {filters.locations.length > 0 && (
                  <div className="p-3 border-t">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setFilters((prev) => ({ ...prev, locations: [] }))}
                    >
                      Clear All
                    </Button>
                  </div>
                )}
              </PopoverContent>
            </Popover>
          </div>

          {/* Customer Filter */}
          <div className="space-y-2">
            <Label>Customers</Label>
            <Popover open={showCustomerDropdown} onOpenChange={setShowCustomerDropdown}>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left font-normal">
                  {filters.customers.length === 0 ? (
                    <span className="text-muted-foreground">All Customers</span>
                  ) : (
                    <span>{filters.customers.length} customer(s) selected</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0" align="start">
                <div className="p-3 border-b">
                  <div className="text-sm font-medium">Select Customers</div>
                </div>
                <div className="max-h-60 overflow-y-auto">
                  {customerOptions.map((customer) => (
                    <div
                      key={customer.value}
                      className="flex items-center space-x-2 p-2 hover:bg-muted cursor-pointer"
                      onClick={() => handleMultiSelectToggle(customer.value, 'customers')}
                    >
                      <Checkbox
                        checked={filters.customers.includes(customer.value)}
                        onChange={() => handleMultiSelectToggle(customer.value, 'customers')}
                      />
                      <span className="text-sm">{customer.label}</span>
                    </div>
                  ))}
                </div>
                {filters.customers.length > 0 && (
                  <div className="p-3 border-t">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setFilters((prev) => ({ ...prev, customers: [] }))}
                    >
                      Clear All
                    </Button>
                  </div>
                )}
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Secondary Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Project Status Filter */}
          <div className="space-y-2">
            <Label>Project Status</Label>
            <Popover open={showStatusDropdown} onOpenChange={setShowStatusDropdown}>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left font-normal">
                  {filters.projectStatus.length === 0 ? (
                    <span className="text-muted-foreground">All Statuses</span>
                  ) : (
                    <span>{filters.projectStatus.length} status(es) selected</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0" align="start">
                <div className="p-3 border-b">
                  <div className="text-sm font-medium">Select Project Status</div>
                </div>
                <div className="max-h-60 overflow-y-auto">
                  {projectStatusOptions.map((status) => (
                    <div
                      key={status.value}
                      className="flex items-center space-x-2 p-2 hover:bg-muted cursor-pointer"
                      onClick={() => handleMultiSelectToggle(status.value, 'projectStatus')}
                    >
                      <Checkbox
                        checked={filters.projectStatus.includes(status.value)}
                        onChange={() => handleMultiSelectToggle(status.value, 'projectStatus')}
                      />
                      <div className="flex items-center gap-2">
                        <Badge className={status.color}>{status.label}</Badge>
                      </div>
                    </div>
                  ))}
                </div>
                {filters.projectStatus.length > 0 && (
                  <div className="p-3 border-t">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setFilters((prev) => ({ ...prev, projectStatus: [] }))}
                    >
                      Clear All
                    </Button>
                  </div>
                )}
              </PopoverContent>
            </Popover>
          </div>

          {/* Amount Range Filter */}
          <div className="space-y-2">
            <Label>Amount Range</Label>
            <div className="flex gap-2">
              <div className="flex-1">
                <Input
                  type="number"
                  placeholder="Min amount"
                  value={filters.amountMin || ''}
                  onChange={(e) => handleAmountChange('amountMin', e.target.value)}
                />
              </div>
              <div className="flex-1">
                <Input
                  type="number"
                  placeholder="Max amount"
                  value={filters.amountMax || ''}
                  onChange={(e) => handleAmountChange('amountMax', e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {(filters.dateRange !== 'All Data' ||
          filters.locations.length > 0 ||
          filters.customers.length > 0 ||
          filters.projectStatus.length > 0 ||
          filters.amountMin !== undefined ||
          filters.amountMax !== undefined) && (
          <div className="space-y-2">
            <Label>Active Filters</Label>
            <div className="flex flex-wrap gap-2">
              {filters.dateRange !== 'All Data' && (
                <Badge variant="secondary" className="gap-1">
                  Date: {filters.dateRange}
                  <button
                    onClick={() =>
                      setFilters((prev) => ({ ...prev, dateRange: 'All Data', customDateRange: undefined }))
                    }
                    className="ml-1 hover:bg-muted rounded-full p-0.5"
                  >
                    ×
                  </button>
                </Badge>
              )}

              {filters.locations.map((locationValue) => {
                const location = locationOptions.find((loc) => loc.value === locationValue);
                return (
                  <Badge key={locationValue} variant="secondary" className="gap-1">
                    {location?.label}
                    <button
                      onClick={() => handleMultiSelectToggle(locationValue, 'locations')}
                      className="ml-1 hover:bg-muted rounded-full p-0.5"
                    >
                      ×
                    </button>
                  </Badge>
                );
              })}

              {filters.customers.map((customerValue) => {
                const customer = customerOptions.find((cust) => cust.value === customerValue);
                return (
                  <Badge key={customerValue} variant="secondary" className="gap-1">
                    {customer?.label}
                    <button
                      onClick={() => handleMultiSelectToggle(customerValue, 'customers')}
                      className="ml-1 hover:bg-muted rounded-full p-0.5"
                    >
                      ×
                    </button>
                  </Badge>
                );
              })}

              {filters.projectStatus.map((statusValue) => {
                const status = projectStatusOptions.find((stat) => stat.value === statusValue);
                return (
                  <Badge key={statusValue} variant="secondary" className="gap-1">
                    {status?.label}
                    <button
                      onClick={() => handleMultiSelectToggle(statusValue, 'projectStatus')}
                      className="ml-1 hover:bg-muted rounded-full p-0.5"
                    >
                      ×
                    </button>
                  </Badge>
                );
              })}

              {(filters.amountMin !== undefined || filters.amountMax !== undefined) && (
                <Badge variant="secondary" className="gap-1">
                  Amount: {filters.amountMin ? `$${filters.amountMin.toLocaleString()}` : '$0'} -{' '}
                  {filters.amountMax ? `$${filters.amountMax.toLocaleString()}` : '∞'}
                  <button
                    onClick={() => setFilters((prev) => ({ ...prev, amountMin: undefined, amountMax: undefined }))}
                    className="ml-1 hover:bg-muted rounded-full p-0.5"
                  >
                    ×
                  </button>
                </Badge>
              )}
            </div>
          </div>
        )}

        <Separator />

        {/* Action Buttons */}
        <div className="flex justify-between items-center">
          <div className="flex gap-2">
            <Button onClick={applyFilters} className="bg-blue-600 hover:bg-blue-700">
              Apply Filters
            </Button>
            <Button variant="outline" onClick={clearFilters}>
              Clear All
            </Button>
          </div>

          <div className="text-sm text-muted-foreground">
            {Object.values(filters).some((value) =>
              Array.isArray(value) ? value.length > 0 : value !== undefined && value !== 'All Data',
            )
              ? 'Filters active'
              : 'No filters applied'}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Server component wrapper - Fixed to avoid server function calls during render
export function Filters() {
  // Use empty arrays to avoid dummy data
  return <FiltersClient customerOptions={[]} locationOptions={[]} projectStatusOptions={[]} />;
}
