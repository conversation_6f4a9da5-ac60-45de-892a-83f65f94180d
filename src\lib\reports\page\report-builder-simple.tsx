'use client';

import { useState } from 'react';
import { BarChart3, Fi<PERSON>, <PERSON>ting<PERSON>, Calculator, X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

import { ColumnSelector } from './column-selector';
import { FilterBuilder } from './filter-builder';
import { GroupCalculator } from './group-calculator';

interface ColumnOption {
  id: string;
  label: string;
  category: string;
  selected: boolean;
  description?: string;
}

interface Filter {
  id: string;
  field: string;
  operator: string;
  value: string;
  label: string;
}

interface GroupCalculation {
  id: string;
  operation: string;
  field: string;
  label: string;
}

interface ReportBuilderProps {
  reportTitle: string;
  reportData: Record<string, unknown>[];
  reportId?: string;
  onClose?: () => void;
  onSave?: (reportData: Record<string, unknown>) => void;
}

export function ReportBuilder({ reportTitle, reportId, onClose, onSave }: Report<PERSON>uilderProps) {
  const [activeModal, setActiveModal] = useState<string | null>(null);
  const [selectedColumns, setSelectedColumns] = useState<ColumnOption[]>([]);
  const [filters, setFilters] = useState<Filter[]>([]);
  const [calculations, setCalculations] = useState<GroupCalculation[]>([]);
  const [reportName, setReportName] = useState(reportTitle);
  const [reportDescription, setReportDescription] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  const handleColumnsClick = () => {
    setActiveModal('columns');
  };

  const handleFiltersClick = () => {
    setActiveModal('filters');
  };

  const handleCalculationsClick = () => {
    setActiveModal('calculations');
  };

  const closeModal = () => {
    setActiveModal(null);
  };

  const handleColumnsApply = (columns: ColumnOption[]) => {
    setSelectedColumns(columns);
    closeModal();
  };

  const handleFiltersApply = (newFilters: Filter[]) => {
    setFilters(newFilters);
    closeModal();
  };

  const handleCalculationsApply = (newCalculations: GroupCalculation[]) => {
    setCalculations(newCalculations);
    closeModal();
  };

  const handleSave = async () => {
    if (!reportName.trim()) {
      alert('Please enter a report name');
      return;
    }

    setIsSaving(true);

    try {
      const reportData = {
        name: reportName,
        description: reportDescription,
        type: 'custom' as const,
        columns: selectedColumns,
        filters: filters,
        calculations: calculations,
        isPublic: false,
      };

      if (reportId && reportId !== 'new') {
        // Update existing report
        const response = await fetch(`/api/custom-reports/${reportId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(reportData),
        });

        if (!response.ok) {
          throw new Error('Failed to update report');
        }

        const updatedReport = await response.json();
        onSave?.(updatedReport);
      } else {
        // Create new report
        const response = await fetch('/api/custom-reports', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(reportData),
        });

        if (!response.ok) {
          throw new Error('Failed to save report');
        }

        const savedReport = await response.json();
        onSave?.(savedReport);
      }

      onClose?.();
    } catch (error) {
      alert('Failed to save report. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-4/5 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex-1 mr-4">
              <Input
                value={reportName}
                onChange={(e) => setReportName(e.target.value)}
                placeholder="Enter report name"
                className="text-xl font-semibold"
              />
              <Input
                value={reportDescription}
                onChange={(e) => setReportDescription(e.target.value)}
                placeholder="Enter report description (optional)"
                className="mt-2 text-sm"
              />
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-auto">
          <div className="text-center py-12">
            <BarChart3 className="h-16 w-16 text-blue-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Report Builder</h3>
            <p className="text-gray-600 mb-6">Professional report configuration interface</p>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
              <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={handleColumnsClick}>
                <Settings className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <h4 className="font-medium text-sm">Columns</h4>
                <p className="text-xs text-gray-500">Configure columns</p>
                {selectedColumns.length > 0 && (
                  <div className="mt-2 text-xs text-blue-600 font-medium">{selectedColumns.length} selected</div>
                )}
              </Card>

              <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={handleFiltersClick}>
                <Filter className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <h4 className="font-medium text-sm">Filters</h4>
                <p className="text-xs text-gray-500">Add filters</p>
                {filters.length > 0 && (
                  <div className="mt-2 text-xs text-green-600 font-medium">{filters.length} active</div>
                )}
              </Card>

              <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={handleCalculationsClick}>
                <Calculator className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <h4 className="font-medium text-sm">Calculations</h4>
                <p className="text-xs text-gray-500">Group calculations</p>
                {calculations.length > 0 && (
                  <div className="mt-2 text-xs text-purple-600 font-medium">{calculations.length} configured</div>
                )}
              </Card>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? 'Saving...' : 'Save Report'}
          </Button>
        </div>
      </div>

      {/* Modal Components */}
      {activeModal === 'columns' && <ColumnSelector onClose={closeModal} onApply={handleColumnsApply} />}

      {activeModal === 'filters' && <FilterBuilder onClose={closeModal} onApply={handleFiltersApply} />}

      {activeModal === 'calculations' && <GroupCalculator onClose={closeModal} onApply={handleCalculationsApply} />}
    </div>
  );
}
