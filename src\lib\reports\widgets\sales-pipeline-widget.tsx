import { Card, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { getProjectModel } from '@/schemas';
import { validateRequest } from '@/server';
import { PROJECTMILESTONE } from '@/schemas/projects/subdocuments';

export async function SalesPipelineWidget() {
  const { user, account } = await validateRequest();
  if (!user || !account) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Sales Pipeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-muted-foreground">Unable to load pipeline data</div>
        </CardContent>
      </Card>
    );
  }

  const projectModel = await getProjectModel();

  const [totalProjects, activeProjects, completedProjects] = await Promise.all([
    projectModel.countDocuments({ account: account._id }),
    projectModel.countDocuments({
      account: account._id,
      isDead: { $ne: true },
      isCancelled: { $ne: true },
      'milestones.name': { $ne: PROJECTMILESTONE.COMPLETED },
    }),
    projectModel.countDocuments({
      account: account._id,
      'milestones.name': PROJECTMILESTONE.COMPLETED,
    }),
  ]);

  const pipelineStages = [
    { stage: 'Total Projects', count: totalProjects, value: 'All Time', percentage: 100 },
    {
      stage: 'Active Projects',
      count: activeProjects,
      value: 'In Progress',
      percentage: totalProjects > 0 ? Math.round((activeProjects / totalProjects) * 100) : 0,
    },
    {
      stage: 'Completed',
      count: completedProjects,
      value: 'Finished',
      percentage: totalProjects > 0 ? Math.round((completedProjects / totalProjects) * 100) : 0,
    },
    {
      stage: 'Success Rate',
      count: totalProjects > 0 ? Math.round((completedProjects / totalProjects) * 100) : 0,
      value: '%',
      percentage: totalProjects > 0 ? Math.round((completedProjects / totalProjects) * 100) : 0,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Sales Pipeline</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {pipelineStages.map((stage) => (
            <div key={stage.stage} className="space-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">{stage.stage}</p>
                  <p className="text-xs text-muted-foreground">{stage.count} items</p>
                </div>
                <p className="text-sm font-bold">{stage.value}</p>
              </div>
              <Progress value={stage.percentage} className="h-2" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
