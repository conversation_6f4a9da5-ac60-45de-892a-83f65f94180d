import { cache } from 'react';

import { getProjectModel, getUserModel, getCustomerModel } from '@/schemas';
import { validateRequest } from '@/server';
import { PROJECTMILESTONE } from '@/schemas/projects/subdocuments';
import { formatCurrency } from '@/lib/formatting';

export const getDashboardData = cache(async function () {
  'use server';

  const { user, account } = await validateRequest();
  if (!user || !account) {
    throw new Error('Unauthorized');
  }

  const projectModel = await getProjectModel();
  const userModel = await getUserModel();
  const customerModel = await getCustomerModel();

  // Get current month date range
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

  // Get last month for comparison
  const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

  try {
    // Fetch all data in parallel
    const [
      totalProjects,
      activeProjects,
      completedProjects,
      totalUsers,
      totalCustomers,
      projectsThisMonth,
      projectsLastMonth,
      recentProjects,
    ] = await Promise.all([
      // Total projects count
      projectModel.countDocuments({ account: account._id }),

      // Active projects (not completed, not dead, not cancelled)
      projectModel.countDocuments({
        account: account._id,
        isDead: { $ne: true },
        isCancelled: { $ne: true },
        'milestones.name': { $ne: PROJECTMILESTONE.COMPLETED },
      }),

      // Completed projects
      projectModel.countDocuments({
        account: account._id,
        'milestones.name': PROJECTMILESTONE.COMPLETED,
      }),

      // Total users
      userModel.countDocuments({ account: account._id }),

      // Total customers
      customerModel.countDocuments({ account: account._id }),

      // Projects created this month
      projectModel.countDocuments({
        account: account._id,
        created: { $gte: startOfMonth, $lte: endOfMonth },
      }),

      // Projects created last month
      projectModel.countDocuments({
        account: account._id,
        created: { $gte: startOfLastMonth, $lte: endOfLastMonth },
      }),

      // Recent projects for activity feed
      projectModel
        .find({
          account: account._id,
        })
        .sort({ created: -1 })
        .limit(5)
        .populate(['documents.customer', 'documents.assignedTo']),
    ]);

    // Revenue calculation - will be implemented when revenue tracking is added
    const estimatedRevenue = 0; // No revenue data available yet

    // Calculate month-over-month growth
    const projectGrowth =
      projectsLastMonth > 0 ? (((projectsThisMonth - projectsLastMonth) / projectsLastMonth) * 100).toFixed(1) : '0';

    // Calculate conversion rate (completed vs total)
    const conversionRate = totalProjects > 0 ? ((completedProjects / totalProjects) * 100).toFixed(1) : '0';

    return {
      kpis: {
        totalRevenue: formatCurrency(estimatedRevenue),
        revenueChange: '+0%', // Will be calculated when revenue tracking is implemented
        activeProjects,
        projectsChange: projectsLastMonth > 0 ? `${Number(projectGrowth) >= 0 ? '+' : ''}${projectGrowth}%` : '+0%',
        conversionRate: `${conversionRate}%`,
        conversionChange: '+0%', // Will be calculated from historical conversion data
        teamMembers: totalUsers,
        teamChange: '+0%', // Will be calculated when user growth tracking is implemented
      },
      metrics: {
        totalProjects,
        completedProjects,
        totalCustomers,
        projectsThisMonth,
        projectsLastMonth,
      },
      recentActivity: recentProjects.map((project) => ({
        title: 'Project created',
        description: `${project.name} - ${project.documents?.customer?.name || 'Unknown Customer'}`,
        time: getRelativeTime(project.created),
        type: 'project' as const,
      })),
    };
  } catch (error) {
    // Error fetching dashboard data

    // Return fallback data
    return {
      kpis: {
        totalRevenue: '$0.00',
        revenueChange: '+0%',
        activeProjects: 0,
        projectsChange: '+0%',
        conversionRate: '0%',
        conversionChange: '+0%',
        teamMembers: 0,
        teamChange: '+0%',
      },
      metrics: {
        totalProjects: 0,
        completedProjects: 0,
        totalCustomers: 0,
        projectsThisMonth: 0,
        projectsLastMonth: 0,
      },
      recentActivity: [],
    };
  }
});

function getRelativeTime(date: Date): string {
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

  if (diffInHours < 1) return 'Just now';
  if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;

  const diffInWeeks = Math.floor(diffInDays / 7);
  return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;
}
