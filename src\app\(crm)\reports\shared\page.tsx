import Link from 'next/link';
import { ArrowLeft, Share, Users, Eye, Download, Star, MoreHorizontal, Lock, Globe, FileText } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export default async function SharedReportsPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/report">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Reports
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Shared Reports</h1>
            <p className="text-muted-foreground">Access reports shared across your organization</p>
          </div>
        </div>
        <Button>
          <Share className="h-4 w-4 mr-2" />
          Share Report
        </Button>
      </div>

      {/* Tabs for different sharing categories */}
      <Tabs defaultValue="shared-with-me" className="space-y-4">
        <TabsList>
          <TabsTrigger value="shared-with-me">Shared with Me</TabsTrigger>
          <TabsTrigger value="my-shared">My Shared Reports</TabsTrigger>
          <TabsTrigger value="public">Public Library</TabsTrigger>
          <TabsTrigger value="favorites">Favorites</TabsTrigger>
        </TabsList>

        <TabsContent value="shared-with-me" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Shared Reports Yet</h3>
              <p className="text-muted-foreground mb-4">Reports shared with you will appear here</p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="my-shared" className="space-y-4">
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Shared Reports</h3>
            <p className="text-muted-foreground mb-4">Reports you share will appear here</p>
          </div>
        </TabsContent>

        <TabsContent value="public" className="space-y-4">
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Public Reports</h3>
            <p className="text-muted-foreground mb-4">Public report templates will appear here</p>
          </div>
        </TabsContent>

        <TabsContent value="favorites" className="space-y-4">
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Favorite Reports</h3>
            <p className="text-muted-foreground mb-4">Your favorite reports will appear here</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

interface SharedReportCardProps {
  title: string;
  description: string;
  sharedBy: string;
  sharedByAvatar: string;
  sharedDate: string;
  views: number;
  isBookmarked: boolean;
  visibility: 'team' | 'department' | 'company';
}

function SharedReportCard({
  title,
  description,
  sharedBy,
  sharedByAvatar,
  sharedDate,
  views,
  isBookmarked,
  visibility,
}: SharedReportCardProps) {
  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'team':
        return <Users className="h-3 w-3" />;
      case 'department':
        return <Users className="h-3 w-3" />;
      case 'company':
        return <Globe className="h-3 w-3" />;
      default:
        return <Lock className="h-3 w-3" />;
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg">{title}</CardTitle>
            <CardDescription className="mt-1">{description}</CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>
                <Eye className="h-4 w-4 mr-2" />
                View Report
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Download
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Star className="h-4 w-4 mr-2" />
                {isBookmarked ? 'Remove from Favorites' : 'Add to Favorites'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Avatar className="h-6 w-6">
              <AvatarImage src={sharedByAvatar} />
              <AvatarFallback>
                {sharedBy
                  .split(' ')
                  .map((n) => n[0])
                  .join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm text-muted-foreground">{sharedBy}</span>
          </div>
          <div className="flex items-center space-x-2">
            {getVisibilityIcon(visibility)}
            <Badge variant="secondary" className="text-xs">
              {views} views
            </Badge>
          </div>
        </div>
        <div className="flex items-center justify-between mt-3">
          <span className="text-xs text-muted-foreground">Shared {sharedDate}</span>
          <Button variant="outline" size="sm">
            <Eye className="h-3 w-3 mr-1" />
            View
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

interface MySharedReportCardProps {
  title: string;
  description: string;
  sharedWith: string;
  lastAccessed: string;
  totalViews: number;
  permissions: string;
}

function MySharedReportCard({
  title,
  description,
  sharedWith,
  lastAccessed,
  totalViews,
  permissions,
}: MySharedReportCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg">{title}</CardTitle>
            <CardDescription className="mt-1">{description}</CardDescription>
          </div>
          <Badge variant="outline" className="text-xs">
            {totalViews} views
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Shared with:</span>
            <span>{sharedWith}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Permissions:</span>
            <Badge variant="secondary" className="text-xs">
              {permissions}
            </Badge>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Last accessed:</span>
            <span>{lastAccessed}</span>
          </div>
        </div>
        <div className="flex gap-2 mt-4">
          <Button variant="outline" size="sm" className="flex-1">
            Manage Access
          </Button>
          <Button variant="outline" size="sm" className="flex-1">
            View Report
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

interface PublicReportCardProps {
  title: string;
  description: string;
  author: string;
  category: string;
  downloads: number;
  rating: number;
}

function PublicReportCard({ title, description, author, category, downloads, rating }: PublicReportCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <CardTitle className="text-lg">{title}</CardTitle>
              <Badge variant="outline" className="text-xs">
                {category}
              </Badge>
            </div>
            <CardDescription>{description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Author:</span>
            <span>{author}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Downloads:</span>
            <span>{downloads.toLocaleString()}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Rating:</span>
            <div className="flex items-center space-x-1">
              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
              <span>{rating}</span>
            </div>
          </div>
        </div>
        <Button className="w-full mt-4" size="sm">
          <Download className="h-3 w-3 mr-1" />
          Use Template
        </Button>
      </CardContent>
    </Card>
  );
}

interface FavoriteReportCardProps {
  title: string;
  description: string;
  type: string;
  lastUpdated: string;
  frequency: string;
}

function FavoriteReportCard({ title, description, type, lastUpdated, frequency }: FavoriteReportCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <CardTitle className="text-lg">{title}</CardTitle>
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            </div>
            <CardDescription>{description}</CardDescription>
          </div>
          <Badge variant="outline" className="text-xs">
            {type}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Frequency:</span>
            <span>{frequency}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Last updated:</span>
            <span>{lastUpdated}</span>
          </div>
        </div>
        <Button className="w-full mt-4" size="sm">
          <Eye className="h-3 w-3 mr-1" />
          View Report
        </Button>
      </CardContent>
    </Card>
  );
}
