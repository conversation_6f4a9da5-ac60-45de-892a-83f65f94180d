import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, Share, FileText } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default async function SharedReportsPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/report">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Reports
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Shared Reports</h1>
            <p className="text-muted-foreground">Access reports shared across your organization</p>
          </div>
        </div>
        <Button>
          <Share className="h-4 w-4 mr-2" />
          Share Report
        </Button>
      </div>

      {/* Tabs for different sharing categories */}
      <Tabs defaultValue="shared-with-me" className="space-y-4">
        <TabsList>
          <TabsTrigger value="shared-with-me">Shared with Me</TabsTrigger>
          <TabsTrigger value="my-shared">My Shared Reports</TabsTrigger>
          <TabsTrigger value="public">Public Library</TabsTrigger>
          <TabsTrigger value="favorites">Favorites</TabsTrigger>
        </TabsList>

        <TabsContent value="shared-with-me" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Shared Reports Yet</h3>
              <p className="text-muted-foreground mb-4">Reports shared with you will appear here</p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="my-shared" className="space-y-4">
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Shared Reports</h3>
            <p className="text-muted-foreground mb-4">Reports you share will appear here</p>
          </div>
        </TabsContent>

        <TabsContent value="public" className="space-y-4">
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Public Reports</h3>
            <p className="text-muted-foreground mb-4">Public report templates will appear here</p>
          </div>
        </TabsContent>

        <TabsContent value="favorites" className="space-y-4">
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Favorite Reports</h3>
            <p className="text-muted-foreground mb-4">Your favorite reports will appear here</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}


