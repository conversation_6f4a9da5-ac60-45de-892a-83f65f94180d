'use client';

import { useState } from 'react';
import { X, Calculator } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';

interface GroupCalculation {
  id: string;
  operation: string;
  field: string;
  label: string;
}

interface GroupCalculatorProps {
  onClose: () => void;
  onApply: (calculations: GroupCalculation[]) => void;
}

export function GroupCalculator({ onClose, onApply }: GroupCalculatorProps) {
  const [selectedOperation, setSelectedOperation] = useState('');
  const [selectedField, setSelectedField] = useState('');
  const [customLabel, setCustomLabel] = useState('');
  const [calculations, setCalculations] = useState<GroupCalculation[]>([]);

  const operations = [
    'Average of',
    'Count',
    'Unique count of',
    'Number of Rows',
    'Max value of',
    'Min value of',
    'Sum of',
    'Percent of total of',
    'Percent of sum of',
    'Percent of average of',
    'Opening percentage',
  ];

  const availableFields = [
    'Job Value',
    'Permit Payment Amount',
    'Total Invoiced',
    'Total Collected',
    'Job Number',
    'Customer Count',
    'Days Since Created',
    'Days Since Last Contact',
    'Milestone Progress',
    'Completion Percentage',
    'Revenue Amount',
    'Cost Amount',
    'Profit Margin',
    'Labor Hours',
    'Material Cost',
    'Equipment Cost',
  ];

  const addCalculation = () => {
    if (!selectedOperation || !selectedField) return;

    const defaultLabel = customLabel || `${selectedField} ${selectedOperation}`;

    const newCalculation: GroupCalculation = {
      id: Date.now().toString(),
      operation: selectedOperation,
      field: selectedField,
      label: defaultLabel,
    };

    setCalculations([...calculations, newCalculation]);
    setSelectedOperation('');
    setSelectedField('');
    setCustomLabel('');
  };

  const removeCalculation = (calculationId: string) => {
    setCalculations(calculations.filter((c) => c.id !== calculationId));
  };

  const updateCalculation = (calculationId: string, updates: Partial<GroupCalculation>) => {
    setCalculations(calculations.map((c) => (c.id === calculationId ? { ...c, ...updates } : c)));
  };

  const handleApply = () => {
    onApply(calculations);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl h-4/5 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Calculator className="h-6 w-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">Group Calculation</h2>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="p-6">
              {/* Create Group Calculation Section */}
              <div className="mb-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Create Group Calculation</h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Select Operation</label>
                    <Select value={selectedOperation} onValueChange={setSelectedOperation}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Choose calculation type" />
                      </SelectTrigger>
                      <SelectContent>
                        {operations.map((operation) => (
                          <SelectItem key={operation} value={operation}>
                            {operation}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedOperation && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-medium text-blue-900 mb-3">{selectedOperation}</h4>
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Select Field</label>
                          <Select value={selectedField} onValueChange={setSelectedField}>
                            <SelectTrigger>
                              <SelectValue placeholder="Choose field to calculate" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableFields.map((field) => (
                                <SelectItem key={field} value={field}>
                                  {field}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        {selectedField && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Custom Label (Optional)
                            </label>
                            <Input
                              value={customLabel}
                              onChange={(e) => setCustomLabel(e.target.value)}
                              placeholder={`${selectedField} ${selectedOperation}`}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {selectedOperation && selectedField && (
                    <div className="flex justify-end">
                      <Button onClick={addCalculation} className="bg-blue-600 hover:bg-blue-700">
                        Add Calculation
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {/* Active Calculations */}
              {calculations.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Active Calculations</h3>
                  <div className="space-y-4">
                    {calculations.map((calculation) => (
                      <div key={calculation.id} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <Calculator className="h-5 w-5 text-blue-600" />
                            <h4 className="font-medium text-gray-900">{calculation.label}</h4>
                          </div>
                          <Button variant="ghost" size="sm" onClick={() => removeCalculation(calculation.id)}>
                            <X className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Operation</label>
                            <Select
                              value={calculation.operation}
                              onValueChange={(value) => updateCalculation(calculation.id, { operation: value })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {operations.map((operation) => (
                                  <SelectItem key={operation} value={operation}>
                                    {operation}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Field</label>
                            <Select
                              value={calculation.field}
                              onValueChange={(value) => updateCalculation(calculation.id, { field: value })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {availableFields.map((field) => (
                                  <SelectItem key={field} value={field}>
                                    {field}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="mt-3">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Display Label</label>
                          <Input
                            value={calculation.label}
                            onChange={(e) => updateCalculation(calculation.id, { label: e.target.value })}
                            placeholder="Enter display label"
                          />
                        </div>

                        <div className="mt-3 flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {calculation.operation}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {calculation.field}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Calculation Examples */}
              <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="font-medium text-yellow-900 mb-2">Calculation Examples</h4>
                <div className="text-sm text-yellow-800 space-y-1">
                  <div>
                    • <strong>Sum of Job Value:</strong> Total value of all jobs in the group
                  </div>
                  <div>
                    • <strong>Average of Days Since Created:</strong> Average age of jobs in days
                  </div>
                  <div>
                    • <strong>Count:</strong> Number of records in the group
                  </div>
                  <div>
                    • <strong>Percent of total of Revenue:</strong> Group&#39;s revenue as % of total
                  </div>
                  <div>
                    • <strong>Max value of Completion Percentage:</strong> Highest completion % in group
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleApply} className="bg-blue-600 hover:bg-blue-700">
            Apply Calculations
          </Button>
        </div>
      </div>
    </div>
  );
}
