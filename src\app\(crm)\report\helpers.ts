import { cache } from 'react';

import { getProjectModel, getUserModel } from '@/schemas';
import { validateRequest } from '@/server';

export const getReportsData = cache(async function () {
  'use server';

  const { user, account } = await validateRequest();
  if (!user || !account) {
    throw new Error('Unauthorized');
  }

  const projectModel = await getProjectModel();
  const userModel = await getUserModel();

  // Get basic counts for stats
  const [totalReports, activeUsers, totalProjects] = await Promise.all([
    // Count of available report types (static for now)
    Promise.resolve(10),

    // Count active users in the account
    userModel.countDocuments({ account: account._id }),

    // Count total projects
    projectModel.countDocuments({ account: account._id }),
  ]);

  // Calculate some basic metrics
  const scheduledReports = 0; // Will be implemented when scheduled reports feature is added
  const sharedReports = 0; // Will be implemented when shared reports feature is added

  return {
    stats: {
      totalReports,
      scheduledReports,
      sharedReports,
    },
    counts: {
      activeUsers,
      totalProjects,
    },
  };
});
