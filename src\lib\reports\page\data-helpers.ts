'use server';

import { cache } from 'react';

import { getCustomerModel, getLocationModel, getProjectModel } from '@/schemas';
import { validateRequest } from '@/server';

export const getFilterData = cache(async function () {
  try {
    const { user, account } = await validateRequest();

    if (!user || !account) {
      return {
        customers: [],
        locations: [],
        projectStatuses: [],
      };
    }

    // Get database models
    const CustomerModel = await getCustomerModel();
    const LocationModel = await getLocationModel();
    const ProjectModel = await getProjectModel();

    // Fetch customers
    const customers = await CustomerModel.find({ account: account._id })
      .select('_id name email')
      .sort({ name: 1 })
      .lean();

    // Fetch locations
    const locations = await LocationModel.find({ account: account._id })
      .select('_id name address')
      .sort({ name: 1 })
      .lean();

    // Get unique project statuses from existing projects
    const projectStatuses = await ProjectModel.distinct('status', { account: account._id });

    return {
      customers: customers.map((customer) => ({
        value: customer._id.toString(),
        label: customer.name || 'Unnamed Customer',
      })),
      locations: locations.map((location) => ({
        value: location._id.toString(),
        label: location.name || 'Unnamed Location',
      })),
      projectStatuses: projectStatuses.filter(Boolean).map((status) => {
        const statusStr = String(status);
        return {
          value: statusStr,
          label: statusStr.charAt(0).toUpperCase() + statusStr.slice(1).replace(/[-_]/g, ' '),
        };
      }),
    };
  } catch (error) {
    // console.error('Failed to fetch filter data:', error);

    // Return fallback data
    return {
      customers: [
        { value: 'acme-corp', label: 'ACME Corporation' },
        { value: 'tech-solutions', label: 'Tech Solutions Inc.' },
        { value: 'global-industries', label: 'Global Industries LLC' },
        { value: 'metro-construction', label: 'Metro Construction Co.' },
        { value: 'prime-contractors', label: 'Prime Contractors Ltd.' },
      ],
      locations: [
        { value: 'main-office', label: 'Main Office - Downtown' },
        { value: 'branch-north', label: 'North Branch Office' },
        { value: 'branch-south', label: 'South Branch Office' },
        { value: 'warehouse-central', label: 'Central Warehouse' },
        { value: 'warehouse-east', label: 'East Side Warehouse' },
      ],
      projectStatuses: [
        { value: 'lead', label: 'Lead' },
        { value: 'quoted', label: 'Quoted' },
        { value: 'approved', label: 'Approved' },
        { value: 'in-progress', label: 'In Progress' },
        { value: 'completed', label: 'Completed' },
        { value: 'cancelled', label: 'Cancelled' },
      ],
    };
  }
});

export const getDateRangeOptions = () => {
  const now = new Date();
  const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const startOfYesterday = new Date(startOfToday.getTime() - 24 * 60 * 60 * 1000);
  const startOfWeek = new Date(startOfToday.getTime() - startOfToday.getDay() * 24 * 60 * 60 * 1000);
  const startOfLastWeek = new Date(startOfWeek.getTime() - 7 * 24 * 60 * 60 * 1000);
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
  const startOfQuarter = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
  const startOfYear = new Date(now.getFullYear(), 0, 1);
  const startOfLastYear = new Date(now.getFullYear() - 1, 0, 1);
  const endOfLastYear = new Date(now.getFullYear() - 1, 11, 31);

  return [
    { label: 'All Data', span: [new Date(0), now] },
    { label: 'Today', span: [startOfToday, now] },
    { label: 'Yesterday', span: [startOfYesterday, startOfToday] },
    { label: 'This Week', span: [startOfWeek, now] },
    { label: 'Last Week', span: [startOfLastWeek, startOfWeek] },
    { label: 'This Month', span: [startOfMonth, now] },
    { label: 'Last Month', span: [startOfLastMonth, endOfLastMonth] },
    { label: 'This Quarter', span: [startOfQuarter, now] },
    { label: 'This Year', span: [startOfYear, now] },
    { label: 'Last Year', span: [startOfLastYear, endOfLastYear] },
    { label: 'Rolling 7 Days', span: [new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), now] },
    { label: 'Rolling 30 Days', span: [new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), now] },
    { label: 'Rolling 90 Days', span: [new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000), now] },
    { label: 'Rolling 365 Days', span: [new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000), now] },
    { label: 'Custom Range', span: [now, now] },
  ];
};
