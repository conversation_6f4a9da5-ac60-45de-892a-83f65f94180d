import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';

import { validateRequest } from '@/server';
import { getCustomReportModel } from '@/schemas/custom-reports';

// GET - Fetch a specific custom report
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { user, account } = await validateRequest();

    if (!user || !account) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid report ID' }, { status: 400 });
    }

    const CustomReportModel = await getCustomReportModel();

    const report = await CustomReportModel.findOne({
      _id: params.id,
      account: account._id,
    })
      .populate('createdBy', 'firstName lastName email')
      .populate('modifiedBy', 'firstName lastName email')
      .lean();

    if (!report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Define a type for the populated createdBy field
    type UserPopulated = {
      firstName: string;
      lastName: string;
      email: string;
      [key: string]: unknown;
    };

    // Transform the data to match the frontend interface
    const transformedReport = {
      id: report._id.toString(),
      name: report.name,
      description: report.description || '',
      type: report.type,
      lastModified: report.modified.toLocaleString(),
      createdBy:
        report.createdBy &&
        typeof report.createdBy === 'object' &&
        'firstName' in report.createdBy &&
        'lastName' in report.createdBy
          ? `${(report.createdBy as unknown as UserPopulated).firstName} ${(report.createdBy as unknown as UserPopulated).lastName}`
          : 'Unknown User',
      isPublic: report.isPublic,
      recordCount: 0,
      columns: report.columns,
      filters: report.filters,
      calculations: report.calculations,
    };

    return NextResponse.json(transformedReport);
  } catch (error) {
    // Error fetching custom report
    return NextResponse.json({ error: 'Failed to fetch report' }, { status: 500 });
  }
}

// PUT - Update a custom report
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { user, account } = await validateRequest();

    if (!user || !account) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid report ID' }, { status: 400 });
    }

    const data = await request.json();
    const CustomReportModel = await getCustomReportModel();

    const updatedReport = await CustomReportModel.findOneAndUpdate(
      { _id: params.id, account: account._id },
      {
        name: data.name,
        description: data.description || '',
        type: data.type || 'custom',
        columns: data.columns || [],
        filters: data.filters || [],
        calculations: data.calculations || [],
        isPublic: data.isPublic || false,
        modifiedBy: user._id,
        modified: new Date(),
      },
      { new: true },
    )
      .populate('createdBy', 'firstName lastName email')
      .populate('modifiedBy', 'firstName lastName email');

    if (!updatedReport) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Transform the response to match frontend interface
    const transformedReport = {
      id: updatedReport._id.toString(),
      name: updatedReport.name,
      description: updatedReport.description || '',
      type: updatedReport.type,
      lastModified: updatedReport.modified.toLocaleString(),
      createdBy:
        updatedReport.createdBy &&
        typeof updatedReport.createdBy === 'object' &&
        'firstName' in updatedReport.createdBy &&
        'lastName' in updatedReport.createdBy
          ? `${(updatedReport.createdBy as { firstName: string; lastName: string }).firstName} ${(updatedReport.createdBy as { firstName: string; lastName: string }).lastName}`
          : 'Unknown User',
      isPublic: updatedReport.isPublic,
      recordCount: 0,
      columns: updatedReport.columns,
      filters: updatedReport.filters,
      calculations: updatedReport.calculations,
    };

    return NextResponse.json(transformedReport);
  } catch (error) {
    // console.error('Error updating custom report:', error);
    return NextResponse.json({ error: 'Failed to update report' }, { status: 500 });
  }
}

// DELETE - Delete a custom report
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { user, account } = await validateRequest();

    if (!user || !account) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid report ID' }, { status: 400 });
    }

    const CustomReportModel = await getCustomReportModel();

    const deletedReport = await CustomReportModel.findOneAndDelete({
      _id: params.id,
      account: account._id,
    });

    if (!deletedReport) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Report deleted successfully' });
  } catch (error) {
    // Error deleting custom report
    return NextResponse.json({ error: 'Failed to delete report' }, { status: 500 });
  }
}
