'use client';

import { useState } from 'react';
import { X, Plus } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';

interface FilterOption {
  id: string;
  label: string;
  category: string;
  type: 'text' | 'select' | 'date' | 'number' | 'boolean';
  options?: string[];
}

interface Filter {
  id: string;
  field: string;
  operator: string;
  value: string;
  label: string;
}

interface FilterBuilderProps {
  onClose: () => void;
  onApply: (filters: Filter[]) => void;
}

export function FilterBuilder({ onClose, onApply }: FilterBuilderProps) {
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedField, setSelectedField] = useState('');
  const [filters, setFilters] = useState<Filter[]>([]);

  const filterOptions: FilterOption[] = [
    // Job Information
    {
      id: 'company-location-name',
      label: 'Company Location Name',
      category: 'Job Information',
      type: 'select',
      options: ['Main Office', 'Branch Office', 'Remote'],
    },
    { id: 'job-name', label: 'Job Name', category: 'Job Information', type: 'text' },
    {
      id: 'company-representative',
      label: 'Company Representative',
      category: 'Job Information',
      type: 'select',
      options: ['John Doe', 'Jane Smith', 'Mike Johnson'],
    },
    { id: 'job-number', label: 'Job Number', category: 'Job Information', type: 'text' },
    {
      id: 'job-status',
      label: 'Job Status',
      category: 'Job Information',
      type: 'select',
      options: ['Active', 'Completed', 'On Hold', 'Cancelled'],
    },
    {
      id: 'job-priority',
      label: 'Job Priority',
      category: 'Job Information',
      type: 'select',
      options: ['High', 'Medium', 'Low'],
    },
    {
      id: 'work-type',
      label: 'Work Type',
      category: 'Job Information',
      type: 'select',
      options: ['Roofing', 'Repair', 'Service', 'Warranty'],
    },
    {
      id: 'job-category',
      label: 'Job Category',
      category: 'Job Information',
      type: 'select',
      options: ['Commercial', 'Property Management', 'Insurance'],
    },

    // Documentation
    { id: 'last-message-date', label: 'Last Message Date', category: 'Documentation', type: 'date' },
    { id: 'created-date', label: 'Created Date', category: 'Documentation', type: 'date' },
    { id: 'modified-date', label: 'Last Modified Date', category: 'Documentation', type: 'date' },

    // Milestones
    {
      id: 'current-milestone',
      label: 'Current Milestone',
      category: 'Milestones',
      type: 'select',
      options: ['Lead', 'Prospect', 'Proposal', 'Contract', 'Production'],
    },
    { id: 'lead-milestone-date', label: 'Lead Milestone Date', category: 'Milestones', type: 'date' },
    { id: 'prospect-milestone-date', label: 'Prospect Milestone Date', category: 'Milestones', type: 'date' },

    // Financial
    { id: 'job-value', label: 'Job Value', category: 'Financial', type: 'number' },
    { id: 'permit-payment-amount', label: 'Permit Payment Amount', category: 'Financial', type: 'number' },
    { id: 'total-invoiced', label: 'Total Invoiced', category: 'Financial', type: 'number' },
    { id: 'total-collected', label: 'Total Collected', category: 'Financial', type: 'number' },

    // Insurance
    { id: 'claim-filed', label: 'Claim Filed?', category: 'Insurance', type: 'boolean' },
    { id: 'claim-number', label: 'Claim Number', category: 'Insurance', type: 'text' },
    {
      id: 'insurance-company',
      label: 'Insurance Company',
      category: 'Insurance',
      type: 'select',
      options: ['State Farm', 'Allstate', 'Progressive', 'GEICO'],
    },

    // Customer Information
    { id: 'customer-name', label: 'Customer Name', category: 'Customer Information', type: 'text' },
    { id: 'customer-phone', label: 'Customer Phone', category: 'Customer Information', type: 'text' },
    { id: 'customer-email', label: 'Customer Email', category: 'Customer Information', type: 'text' },
    {
      id: 'customer-type',
      label: 'Customer Type',
      category: 'Customer Information',
      type: 'select',
      options: ['Residential', 'Commercial', 'Property Management'],
    },
  ];

  const categories = Array.from(new Set(filterOptions.map((option) => option.category)));

  const getOperatorOptions = (type: string) => {
    switch (type) {
      case 'text':
        return ['Contains', 'Equals', 'Starts with', 'Ends with', 'Does not contain'];
      case 'number':
        return ['Equals', 'Greater than', 'Less than', 'Between', 'Greater than or equal', 'Less than or equal'];
      case 'date':
        return ['Equals', 'Before', 'After', 'Between', 'In the last', 'More than days ago'];
      case 'select':
        return ['Equals', 'Does not equal', 'In', 'Not in'];
      case 'boolean':
        return ['Is true', 'Is false'];
      default:
        return ['Equals'];
    }
  };

  const addFilter = () => {
    if (!selectedField) return;

    const field = filterOptions.find((f) => f.id === selectedField);
    if (!field) return;

    const newFilter: Filter = {
      id: Date.now().toString(),
      field: selectedField,
      operator: getOperatorOptions(field.type)[0],
      value: '',
      label: field.label,
    };

    setFilters([...filters, newFilter]);
    setSelectedField('');
    setSelectedCategory('');
  };

  const removeFilter = (filterId: string) => {
    setFilters(filters.filter((f) => f.id !== filterId));
  };

  const updateFilter = (filterId: string, updates: Partial<Filter>) => {
    setFilters(filters.map((f) => (f.id === filterId ? { ...f, ...updates } : f)));
  };

  const handleApply = () => {
    onApply(filters);
    onClose();
  };

  const categoryFields = selectedCategory ? filterOptions.filter((option) => option.category === selectedCategory) : [];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl h-4/5 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">Add Filter</h2>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          <div className="mt-4 space-y-2">
            <p className="text-sm text-gray-600">
              You can narrow your results by creating filters. Start by selecting an indexed column.
            </p>
            <p className="text-sm text-gray-600">Then, choose the desired filter type and value if applicable.</p>
            <p className="text-sm text-gray-600">You can only filter on columns that have been added to your report.</p>
          </div>
        </div>

        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="p-6">
              {/* Create a Filter Section */}
              <div className="mb-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Create a Filter</h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Select Column</label>
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select Category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedCategory && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-medium text-blue-900 mb-3">{selectedCategory}</h4>
                      <div className="grid grid-cols-1 gap-2">
                        {categoryFields.map((field) => (
                          <div
                            key={field.id}
                            className={`p-3 rounded cursor-pointer transition-colors ${
                              selectedField === field.id
                                ? 'bg-blue-600 text-white'
                                : 'bg-white hover:bg-blue-100 text-gray-900'
                            }`}
                            onClick={() => setSelectedField(field.id)}
                          >
                            <div className="font-medium">{field.label}</div>
                            <div
                              className={`text-sm ${selectedField === field.id ? 'text-blue-100' : 'text-gray-500'}`}
                            >
                              Type: {field.type}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {selectedField && (
                    <div className="flex justify-end">
                      <Button onClick={addFilter} className="bg-blue-600 hover:bg-blue-700">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Filter
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {/* Active Filters */}
              {filters.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Active Filters</h3>
                  <div className="space-y-4">
                    {filters.map((filter) => {
                      const field = filterOptions.find((f) => f.id === filter.field);
                      if (!field) return null;

                      return (
                        <div key={filter.id} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-medium text-gray-900">{filter.label}</h4>
                            <Button variant="ghost" size="sm" onClick={() => removeFilter(filter.id)}>
                              <X className="h-4 w-4" />
                            </Button>
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Operator</label>
                              <Select
                                value={filter.operator}
                                onValueChange={(value) => updateFilter(filter.id, { operator: value })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {getOperatorOptions(field.type).map((operator) => (
                                    <SelectItem key={operator} value={operator}>
                                      {operator}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Value</label>
                              {field.type === 'select' && field.options ? (
                                <Select
                                  value={filter.value}
                                  onValueChange={(value) => updateFilter(filter.id, { value })}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select value" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {field.options.map((option) => (
                                      <SelectItem key={option} value={option}>
                                        {option}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              ) : field.type === 'boolean' ? (
                                <div className="flex items-center space-x-4 pt-2">
                                  <Badge variant={filter.operator === 'Is true' ? 'default' : 'secondary'}>
                                    {filter.operator}
                                  </Badge>
                                </div>
                              ) : (
                                <Input
                                  type={field.type === 'number' ? 'number' : field.type === 'date' ? 'date' : 'text'}
                                  value={filter.value}
                                  onChange={(e) => updateFilter(filter.id, { value: e.target.value })}
                                  placeholder={`Enter ${field.type} value`}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleApply} className="bg-blue-600 hover:bg-blue-700">
            Apply
          </Button>
        </div>
      </div>
    </div>
  );
}
