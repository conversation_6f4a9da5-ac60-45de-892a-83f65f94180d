import Link from 'next/link';
import { ArrowLeft, Calendar, Clock, Mail, Plus, Settings, Trash2 } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

export default async function ReportSchedulePage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/report">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Reports
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Scheduled Reports</h1>
            <p className="text-muted-foreground">Automate report delivery via email</p>
          </div>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Schedule New Report
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Schedule Configuration */}
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Create Schedule</CardTitle>
              <CardDescription>Set up automatic report delivery</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="schedule-name">Schedule Name</Label>
                <Input id="schedule-name" placeholder="Enter schedule name" />
              </div>

              <div className="space-y-2">
                <Label>Report Type</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select report" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sales">Sales Report</SelectItem>
                    <SelectItem value="profitability">Profitability Report</SelectItem>
                    <SelectItem value="ar-age">A/R Age Report</SelectItem>
                    <SelectItem value="lead-source">Lead Source Report</SelectItem>
                    <SelectItem value="closing-percentage">Closing Percentage</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Frequency</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Delivery Time</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select time" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="06:00">6:00 AM</SelectItem>
                    <SelectItem value="08:00">8:00 AM</SelectItem>
                    <SelectItem value="10:00">10:00 AM</SelectItem>
                    <SelectItem value="12:00">12:00 PM</SelectItem>
                    <SelectItem value="14:00">2:00 PM</SelectItem>
                    <SelectItem value="16:00">4:00 PM</SelectItem>
                    <SelectItem value="18:00">6:00 PM</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>File Format</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="pdf" defaultChecked />
                    <Label htmlFor="pdf" className="text-sm">
                      PDF
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="excel" />
                    <Label htmlFor="excel" className="text-sm">
                      Excel
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="csv" />
                    <Label htmlFor="csv" className="text-sm">
                      CSV
                    </Label>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="recipients">Email Recipients</Label>
                <Input id="recipients" placeholder="Enter email addresses (comma separated)" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="subject">Email Subject</Label>
                <Input id="subject" placeholder="Automated Report: [Report Name]" />
              </div>

              <Button className="w-full">
                <Calendar className="h-4 w-4 mr-2" />
                Create Schedule
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Scheduled Reports List */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Active Schedules</CardTitle>
              <CardDescription>Manage your scheduled report deliveries</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Schedule Name</TableHead>
                    <TableHead>Report Type</TableHead>
                    <TableHead>Frequency</TableHead>
                    <TableHead>Next Run</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <tr>
                    <td colSpan={6} className="text-center py-8 text-muted-foreground">
                      No scheduled reports yet. Create your first schedule above.
                    </td>
                  </tr>
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Deliveries</CardTitle>
              <CardDescription>History of automated report deliveries</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                No delivery history yet. Scheduled reports will appear here once delivered.
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

interface ScheduleRowProps {
  name: string;
  reportType: string;
  frequency: string;
  nextRun: string;
  status: 'Active' | 'Paused';
}

function ScheduleRow({ name, reportType, frequency, nextRun, status }: ScheduleRowProps) {
  return (
    <TableRow>
      <TableCell className="font-medium">{name}</TableCell>
      <TableCell>{reportType}</TableCell>
      <TableCell>{frequency}</TableCell>
      <TableCell>
        <div className="flex items-center space-x-1">
          <Clock className="h-3 w-3 text-muted-foreground" />
          <span className="text-sm">{nextRun}</span>
        </div>
      </TableCell>
      <TableCell>
        <Badge variant={status === 'Active' ? 'default' : 'secondary'}>{status}</Badge>
      </TableCell>
      <TableCell>
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm">
            <Settings className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm">
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );
}

interface DeliveryItemProps {
  reportName: string;
  deliveredTo: string;
  deliveredAt: string;
  status: 'Delivered' | 'Failed';
}

function DeliveryItem({ reportName, deliveredTo, deliveredAt, status }: DeliveryItemProps) {
  return (
    <div className="flex items-start justify-between p-4 border rounded-lg">
      <div className="flex-1">
        <div className="flex items-center space-x-2 mb-1">
          <h4 className="font-medium">{reportName}</h4>
          <Badge variant={status === 'Delivered' ? 'default' : 'destructive'} className="text-xs">
            {status}
          </Badge>
        </div>
        <div className="flex items-center space-x-1 text-sm text-muted-foreground mb-1">
          <Mail className="h-3 w-3" />
          <span>{deliveredTo}</span>
        </div>
        <p className="text-xs text-muted-foreground">{deliveredAt}</p>
      </div>
      <Button variant="ghost" size="sm">
        View Details
      </Button>
    </div>
  );
}
