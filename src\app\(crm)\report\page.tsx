import Link from 'next/link';
import { Bar<PERSON>hart3, Calendar, FileText, <PERSON><PERSON>hart, <PERSON><PERSON>s, TrendingUp, Users, BookOpen } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { validatePermissions } from './permissions';
import { getReportsData } from './helpers';

export default async function ReportsPage() {
  await validatePermissions();
  const data = await getReportsData();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-3xl font-bold">Reports & Analytics</CardTitle>
          <CardDescription className="text-lg mt-2">
            Access comprehensive business insights and performance metrics
          </CardDescription>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href="/reports/dashboard">
              <BarChart3 className="h-4 w-4 mr-2" />
              Dashboard
            </Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/reports/shared">
              <Users className="h-4 w-4 mr-2" />
              Shared Reports
            </Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/reports/schedule">
              <Calendar className="h-4 w-4 mr-2" />
              Scheduled Reports
            </Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/reports/glossary">
              <BookOpen className="h-4 w-4 mr-2" />
              Glossary
            </Link>
          </Button>
          <Button asChild>
            <Link href="/reports/custom">
              <FileText className="h-4 w-4 mr-2" />
              Create Report
            </Link>
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.stats.totalReports}</div>
            <p className="text-xs text-muted-foreground">Available report types</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduled Reports</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.stats.scheduledReports}</div>
            <p className="text-xs text-muted-foreground">Active schedules</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Shared Reports</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.stats.sharedReports}</div>
            <p className="text-xs text-muted-foreground">Across teams</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.counts.activeUsers}</div>
            <p className="text-xs text-muted-foreground">Team members</p>
          </CardContent>
        </Card>
      </div>

      {/* Reports Tabs */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Reports</TabsTrigger>
          <TabsTrigger value="sales">Sales & Marketing</TabsTrigger>
          <TabsTrigger value="production">Production</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="custom">Custom Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <ReportCard
              title="Closing Percentage Report"
              description="Track conversion rates by sales representative"
              href="/report/closing-percentage"
              category="Sales"
              icon={<PieChart className="h-5 w-5" />}
            />
            <ReportCard
              title="Dead Leads Report"
              description="Analyze lost opportunities and reasons"
              href="/report/dead-leads"
              category="Sales"
              icon={<BarChart3 className="h-5 w-5" />}
            />
            <ReportCard
              title="Lead Source Report"
              description="Performance by marketing channel"
              href="/report/lead-source"
              category="Marketing"
              icon={<TrendingUp className="h-5 w-5" />}
            />
            <ReportCard
              title="A/R Age Report"
              description="Outstanding receivables analysis"
              href="/report/ar-age"
              category="Financial"
              icon={<FileText className="h-5 w-5" />}
            />
            <ReportCard
              title="Job Age Report"
              description="Project timeline and duration analysis"
              href="/report/job-age"
              category="Production"
              icon={<Calendar className="h-5 w-5" />}
            />
            <ReportCard
              title="Profitability Report"
              description="Project margins and profitability"
              href="/report/profitability"
              category="Financial"
              icon={<TrendingUp className="h-5 w-5" />}
            />
          </div>
        </TabsContent>

        <TabsContent value="sales" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <ReportCard
              title="Closing Percentage Report"
              description="Track conversion rates by sales representative"
              href="/report/closing-percentage"
              category="Sales"
              icon={<PieChart className="h-5 w-5" />}
            />
            <ReportCard
              title="Dead Leads Report"
              description="Analyze lost opportunities and reasons"
              href="/report/dead-leads"
              category="Sales"
              icon={<BarChart3 className="h-5 w-5" />}
            />
            <ReportCard
              title="Lead Source Report"
              description="Performance by marketing channel"
              href="/report/lead-source"
              category="Marketing"
              icon={<TrendingUp className="h-5 w-5" />}
            />
            <ReportCard
              title="Sales Report"
              description="Comprehensive sales performance metrics"
              href="/report/sales"
              category="Sales"
              icon={<BarChart3 className="h-5 w-5" />}
            />
          </div>
        </TabsContent>

        <TabsContent value="production" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <ReportCard
              title="Job Age Report"
              description="Project timeline and duration analysis"
              href="/report/job-age"
              category="Production"
              icon={<Calendar className="h-5 w-5" />}
            />
            <ReportCard
              title="Permits Report"
              description="Permit status and processing times"
              href="/report/permits"
              category="Production"
              icon={<FileText className="h-5 w-5" />}
            />
          </div>
        </TabsContent>

        <TabsContent value="financial" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <ReportCard
              title="A/R Age Report"
              description="Outstanding receivables analysis"
              href="/report/ar-age"
              category="Financial"
              icon={<FileText className="h-5 w-5" />}
            />
            <ReportCard
              title="Expenses Report"
              description="Cost analysis and expense tracking"
              href="/report/expenses"
              category="Financial"
              icon={<BarChart3 className="h-5 w-5" />}
            />
            <ReportCard
              title="Payments Received Report"
              description="Payment history and cash flow"
              href="/report/payments-received"
              category="Financial"
              icon={<TrendingUp className="h-5 w-5" />}
            />
            <ReportCard
              title="Profitability Report"
              description="Project margins and profitability"
              href="/report/profitability"
              category="Financial"
              icon={<TrendingUp className="h-5 w-5" />}
            />
          </div>
        </TabsContent>

        <TabsContent value="custom" className="space-y-4">
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Custom Reports Yet</h3>
            <p className="text-muted-foreground mb-4">Create your first custom report to get started</p>
            <Button asChild>
              <Link href="/reports/custom">
                <FileText className="h-4 w-4 mr-2" />
                Create Custom Report
              </Link>
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

interface ReportCardProps {
  title: string;
  description: string;
  href: string;
  category: string;
  icon: React.ReactNode;
  lastUpdated?: string;
}

function ReportCard({ title, description, href, category, icon, lastUpdated }: ReportCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {icon}
            <Badge variant="secondary" className="text-xs">
              {category}
            </Badge>
          </div>
          <Button variant="ghost" size="sm" asChild>
            <Link href={href}>
              <Settings className="h-4 w-4" />
            </Link>
          </Button>
        </div>
        <CardTitle className="text-lg">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          {lastUpdated && <span>Updated {lastUpdated}</span>}
          <Button variant="outline" size="sm" asChild>
            <Link href={href}>View Report</Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
