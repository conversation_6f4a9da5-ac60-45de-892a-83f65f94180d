import { NextRequest, NextResponse } from 'next/server';
import { validateRequest } from '@/server';
import { getProjectModel, getCustomerModel, getLocationModel } from '@/schemas';

export async function GET(request: NextRequest) {
  try {
    const { user, account } = await validateRequest();

    if (!user || !account) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const format = searchParams.get('format') || 'csv';

    // Parse filter parameters
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : new Date(0);
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : new Date();
    const locations = searchParams.get('locations')?.split(',').filter(Boolean) || [];
    const customers = searchParams.get('customers')?.split(',').filter(Boolean) || [];
    const projectStatus = searchParams.get('projectStatus')?.split(',').filter(Boolean) || [];
    const amountMin = searchParams.get('amountMin') ? Number(searchParams.get('amountMin')) : undefined;
    const amountMax = searchParams.get('amountMax') ? Number(searchParams.get('amountMax')) : undefined;

    // Get database models
    const ProjectModel = await getProjectModel();
    const CustomerModel = await getCustomerModel();
    const LocationModel = await getLocationModel();

    // Build database query filters
    const dbFilters: any = {
      account: account._id,
      createdAt: { $gte: startDate, $lte: endDate },
    };

    // Add location filter
    if (locations.length > 0) {
      const locationDocs = await LocationModel.find({
        account: account._id,
        $or: [{ name: { $in: locations } }, { _id: { $in: locations.filter((id) => id.match(/^[0-9a-fA-F]{24}$/)) } }],
      });
      if (locationDocs.length > 0) {
        dbFilters.location = { $in: locationDocs.map((loc) => loc._id) };
      }
    }

    // Add customer filter
    if (customers.length > 0) {
      const customerDocs = await CustomerModel.find({
        account: account._id,
        $or: [{ name: { $in: customers } }, { _id: { $in: customers.filter((id) => id.match(/^[0-9a-fA-F]{24}$/)) } }],
      });
      if (customerDocs.length > 0) {
        dbFilters.customer = { $in: customerDocs.map((cust) => cust._id) };
      }
    }

    // Add project status filter
    if (projectStatus.length > 0) {
      dbFilters.status = { $in: projectStatus };
    }

    // Add amount range filter
    if (amountMin !== undefined || amountMax !== undefined) {
      dbFilters.totalAmount = {};
      if (amountMin !== undefined) dbFilters.totalAmount.$gte = amountMin;
      if (amountMax !== undefined) dbFilters.totalAmount.$lte = amountMax;
    }

    // Fetch projects with filters
    const projects = await ProjectModel.find(dbFilters)
      .populate('customer', 'name email')
      .populate('location', 'name address')
      .sort({ createdAt: -1 })
      .lean();

    if (format === 'csv') {
      return generateCSVResponse(projects);
    } else if (format === 'pdf') {
      return generatePDFResponse(projects);
    } else {
      return NextResponse.json({ error: 'Invalid format' }, { status: 400 });
    }
  } catch (error) {
    console.error('Export error:', error);
    return NextResponse.json({ error: 'Export failed' }, { status: 500 });
  }
}

function generateCSVResponse(projects: any[]) {
  const headers = [
    'Date Created',
    'Customer',
    'Project Name',
    'Status',
    'Total Amount',
    'Location',
    'Age (Days)',
    'Description',
  ];

  const csvRows = [headers.join(',')];

  const now = new Date();
  projects.forEach((project) => {
    const ageInDays = Math.floor((now.getTime() - new Date(project.createdAt).getTime()) / (1000 * 60 * 60 * 24));
    const row = [
      new Date(project.createdAt).toLocaleDateString(),
      project.customer?.name || 'Unknown Customer',
      `"${project.name || 'Unnamed Project'}"`,
      project.status || 'Unknown',
      `$${(project.totalAmount || 0).toLocaleString()}`,
      project.location?.name || 'Unknown Location',
      ageInDays.toString(),
      `"${project.description || ''}"`,
    ];
    csvRows.push(row.join(','));
  });

  const csvContent = csvRows.join('\n');
  const timestamp = new Date().toISOString().split('T')[0];

  return new NextResponse(csvContent, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="report-${timestamp}.csv"`,
    },
  });
}

function generatePDFResponse(projects: any[]) {
  // For PDF generation, you would typically use a library like puppeteer, jsPDF, or PDFKit
  // This is a simplified implementation that returns a text-based "PDF"

  const pdfContent = `
REPORT EXPORT
Generated: ${new Date().toLocaleString()}
Total Projects: ${projects.length}

${projects
  .map(
    (project, index) => `
${index + 1}. ${project.name || 'Unnamed Project'}
   Customer: ${project.customer?.name || 'Unknown'}
   Status: ${project.status || 'Unknown'}
   Amount: $${(project.totalAmount || 0).toLocaleString()}
   Location: ${project.location?.name || 'Unknown'}
   Created: ${new Date(project.createdAt).toLocaleDateString()}
`,
  )
  .join('\n')}
`;

  const timestamp = new Date().toISOString().split('T')[0];

  return new NextResponse(pdfContent, {
    headers: {
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="report-${timestamp}.pdf"`,
    },
  });
}
