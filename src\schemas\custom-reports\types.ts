import mongoose from 'mongoose';
import type { Account } from '@/schemas/accounts';
import type { User } from '@/schemas/users';

export interface ColumnOption {
  id: string;
  label: string;
  category: string;
  selected: boolean;
  description?: string;
}

export interface Filter {
  id: string;
  field: string;
  operator: string;
  value: string;
  label: string;
}

export interface GroupCalculation {
  id: string;
  operation: string;
  field: string;
  label: string;
}

export interface CustomReport {
  _id: mongoose.Types.ObjectId;
  account: mongoose.Types.ObjectId;
  name: string;
  description: string;
  type: 'financial' | 'operational' | 'sales' | 'custom';
  columns: ColumnOption[];
  filters: Filter[];
  calculations: GroupCalculation[];
  isPublic: boolean;
  createdBy: mongoose.Types.ObjectId;
  modifiedBy: mongoose.Types.ObjectId;
  created: Date;
  modified: Date;
  documents?: {
    account?: Account;
    createdBy?: User;
    modifiedBy?: User;
  };
}

export type CustomReportDoc = mongoose.Document<mongoose.Types.ObjectId, object, CustomReport> & CustomReport;

export type CustomReportModel = mongoose.Model<CustomReport>;
