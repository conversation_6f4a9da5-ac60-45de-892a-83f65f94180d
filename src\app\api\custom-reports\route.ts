import { NextRequest, NextResponse } from 'next/server';

import { validateRequest } from '@/server';
import { getCustomReportModel } from '@/schemas/custom-reports';

// GET - Fetch all custom reports for the account
export async function GET() {
  try {
    const { user, account } = await validateRequest();

    if (!user || !account) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const CustomReportModel = await getCustomReportModel();

    const reports = await CustomReportModel.find({ account: account._id })
      .populate('createdBy', 'firstName lastName email')
      .populate('modifiedBy', 'firstName lastName email')
      .sort({ modified: -1 })
      .lean();

    // Transform the data to match the frontend interface
    const transformedReports = reports.map((report) => ({
      id: report._id.toString(),
      name: report.name,
      description: report.description || '',
      type: report.type,
      lastModified: report.modified.toLocaleString(),
      createdBy: report.createdBy ? `${report.createdBy.firstName} ${report.createdBy.lastName}` : 'Unknown User',
      isPublic: report.isPublic,
      recordCount: 0, // This would be calculated based on the actual data
      columns: report.columns,
      filters: report.filters,
      calculations: report.calculations,
    }));

    return NextResponse.json(transformedReports);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch reports' }, { status: 500 });
  }
}

// POST - Create a new custom report
export async function POST(request: NextRequest) {
  try {
    const { user, account } = await validateRequest();

    if (!user || !account) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const CustomReportModel = await getCustomReportModel();

    const newReport = new CustomReportModel({
      account: account._id,
      name: data.name,
      description: data.description || '',
      type: data.type || 'custom',
      columns: data.columns || [],
      filters: data.filters || [],
      calculations: data.calculations || [],
      isPublic: data.isPublic || false,
      createdBy: user._id,
      modifiedBy: user._id,
    });

    const savedReport = await newReport.save();

    // Populate the user data for response
    await savedReport.populate('createdBy', 'firstName lastName email');
    await savedReport.populate('modifiedBy', 'firstName lastName email');

    // Transform the response to match frontend interface
    const transformedReport = {
      id: savedReport._id.toString(),
      name: savedReport.name,
      description: savedReport.description || '',
      type: savedReport.type,
      lastModified: savedReport.modified.toLocaleString(),
      createdBy: savedReport.createdBy
        ? `${savedReport.createdBy.firstName} ${savedReport.createdBy.lastName}`
        : 'Unknown User',
      isPublic: savedReport.isPublic,
      recordCount: 0,
      columns: savedReport.columns,
      filters: savedReport.filters,
      calculations: savedReport.calculations,
    };

    return NextResponse.json(transformedReport, { status: 201 });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create report' }, { status: 500 });
  }
}
